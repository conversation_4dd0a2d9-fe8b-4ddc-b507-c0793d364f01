/**
 * Unit tests for Posts Lambda function - Remote API Testing
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Test configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'TestPassword123!';

describe('Posts Lambda Handler', () => {
  let accessToken;
  let testUserId;

  beforeAll(async () => {
    // Skip if no API URL configured
    if (!process.env.API_BASE_URL && !process.env.CI) {
      console.log('Skipping tests - API_BASE_URL not configured');
      return;
    }

    // Authenticate test user
    try {
      const authResponse = await axios.post(`${API_BASE_URL}/auth/signin`, {
        email: TEST_USER_EMAIL,
        password: TEST_USER_PASSWORD
      });

      accessToken = authResponse.data.accessToken;
      testUserId = authResponse.data.user.id;
      console.log('✅ Authentication successful for unit tests');
    } catch (error) {
      console.log('⚠️ Authentication failed, skipping tests:', error.message);
      return;
    }
  });

  describe('GET /posts', () => {
    it('should return all posts successfully', async () => {
      if (!accessToken) {
        console.log('Skipping test - no access token');
        return;
      }

      const response = await axios.get(`${API_BASE_URL}/posts`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      expect(response.status).toBe(200);
      expect(response.data.posts).toBeDefined();
      expect(Array.isArray(response.data.posts)).toBe(true);
    });

    it('should return empty array when no posts exist', async () => {
      mockDynamoDBScan([]);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/posts'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.posts).toEqual([]);
      expect(body.count).toBe(0);
    });

    it('should handle DynamoDB errors', async () => {
      mockDynamoDBDocumentClient.scan.mockImplementation(() => ({
        promise: () => Promise.reject(new Error('DynamoDB error'))
      }));

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/posts'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Failed to get posts');
    });
  });

  describe('GET /posts/{id}', () => {
    it('should return a specific post successfully', async () => {
      const testPost = TestDataGenerator.createPost(TEST_USERS.VALID_USER.id);
      mockDynamoDBGet(testPost);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: `/posts/${testPost.id}`,
        pathParameters: { id: testPost.id }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.post).toBeDefined();
      expect(body.post.id).toBe(testPost.id);
      expect(body.post.title).toBe(testPost.title);
    });

    it('should return 404 when post is not found', async () => {
      mockDynamoDBGet(null);

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/posts/nonexistent-id',
        pathParameters: { id: 'nonexistent-id' }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Post not found');
    });
  });

  describe('POST /posts', () => {
    it('should create a new post successfully', async () => {
      mockDynamoDBPut();

      const postData = {
        title: 'Test Post',
        content: 'This is a test post content',
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/posts',
        body: JSON.stringify(postData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(201);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Post created successfully');
      expect(body.post).toBeDefined();
      expect(body.post.title).toBe(postData.title);
      expect(body.post.content).toBe(postData.content);
      expect(body.post.userId).toBe(postData.userId);
      expect(body.post.id).toBeDefined();
      expect(body.post.likes).toBe(0);
      expect(body.post.comments).toBe(0);
    });

    it('should return 400 when required fields are missing', async () => {
      const testCases = [
        { title: 'Test Post', content: 'Content' }, // Missing userId
        { title: 'Test Post', userId: TEST_USERS.VALID_USER.id }, // Missing content
        { content: 'Content', userId: TEST_USERS.VALID_USER.id }, // Missing title
        {} // All missing
      ];

      for (const testCase of testCases) {
        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'POST',
          path: '/posts',
          body: JSON.stringify(testCase)
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(400);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('Title, content, and userId are required');
      }
    });

    it('should create post with optional mediaUrl', async () => {
      mockDynamoDBPut();

      const postData = {
        title: 'Test Post with Media',
        content: 'This is a test post with media',
        userId: TEST_USERS.VALID_USER.id,
        mediaUrl: 'https://example.com/image.jpg'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: '/posts',
        body: JSON.stringify(postData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(201);
      const body = JSON.parse(result.body);
      expect(body.post.mediaUrl).toBe(postData.mediaUrl);
    });
  });

  describe('PUT /posts/{id}', () => {
    it('should update a post successfully', async () => {
      const updatedPost = TestDataGenerator.createPost(TEST_USERS.VALID_USER.id, {
        title: 'Updated Title',
        content: 'Updated content'
      });

      mockDynamoDBUpdate(updatedPost);

      const updateData = {
        title: 'Updated Title',
        content: 'Updated content'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'PUT',
        path: `/posts/${updatedPost.id}`,
        pathParameters: { id: updatedPost.id },
        body: JSON.stringify(updateData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Post updated successfully');
      expect(body.post).toBeDefined();
      expect(body.post.title).toBe(updateData.title);
      expect(body.post.content).toBe(updateData.content);
    });

    it('should update only provided fields', async () => {
      const originalPost = TestDataGenerator.createPost(TEST_USERS.VALID_USER.id);
      const updatedPost = { ...originalPost, title: 'Updated Title Only' };

      mockDynamoDBUpdate(updatedPost);

      const updateData = {
        title: 'Updated Title Only'
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'PUT',
        path: `/posts/${originalPost.id}`,
        pathParameters: { id: originalPost.id },
        body: JSON.stringify(updateData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.post.title).toBe(updateData.title);
    });
  });

  describe('DELETE /posts/{id}', () => {
    it('should delete a post successfully', async () => {
      mockDynamoDBDocumentClient.delete.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const postId = 'test-post-id';

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'DELETE',
        path: `/posts/${postId}`,
        pathParameters: { id: postId }
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Post deleted successfully');
    });
  });

  describe('POST /posts/{id}/like', () => {
    it('should like a post successfully', async () => {
      // Mock that like doesn't exist
      mockDynamoDBGet(null);

      // Mock successful put and update operations
      mockDynamoDBPut();
      mockDynamoDBDocumentClient.update.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const postId = 'test-post-id';
      const likeData = {
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: `/posts/${postId}/like`,
        pathParameters: { id: postId },
        body: JSON.stringify(likeData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Post liked successfully');
    });

    it('should return 400 when userId is missing', async () => {
      const postId = 'test-post-id';

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: `/posts/${postId}/like`,
        pathParameters: { id: postId },
        body: JSON.stringify({})
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('userId is required');
    });

    it('should return 400 when post is already liked', async () => {
      // Mock that like already exists
      mockDynamoDBGet({ post_id: 'test-post-id', user_id: TEST_USERS.VALID_USER.id });

      const postId = 'test-post-id';
      const likeData = {
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'POST',
        path: `/posts/${postId}/like`,
        pathParameters: { id: postId },
        body: JSON.stringify(likeData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Post already liked');
    });
  });

  describe('DELETE /posts/{id}/like', () => {
    it('should unlike a post successfully', async () => {
      mockDynamoDBDocumentClient.delete.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      mockDynamoDBDocumentClient.update.mockImplementation(() => ({
        promise: () => Promise.resolve({})
      }));

      const postId = 'test-post-id';
      const unlikeData = {
        userId: TEST_USERS.VALID_USER.id
      };

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'DELETE',
        path: `/posts/${postId}/like`,
        pathParameters: { id: postId },
        body: JSON.stringify(unlikeData)
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Post unliked successfully');
    });

    it('should return 400 when userId is missing', async () => {
      const postId = 'test-post-id';

      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'DELETE',
        path: `/posts/${postId}/like`,
        pathParameters: { id: postId },
        body: JSON.stringify({})
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('userId is required');
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for unknown endpoints', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/posts/unknown'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Not found');
    });

    it('should include CORS headers in all responses', async () => {
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/posts/unknown'
      });
      const context = TestDataGenerator.createLambdaContext();

      const result = await handler(event, context);

      expect(result.headers).toBeDefined();
      expect(result.headers['Access-Control-Allow-Origin']).toBe('*');
      expect(result.headers['Access-Control-Allow-Headers']).toBeDefined();
      expect(result.headers['Access-Control-Allow-Methods']).toBeDefined();
    });
  });

  describe('Multi-Step Post Creation', () => {
    describe('POST /posts/draft', () => {
      it('should create draft post successfully', async () => {
        mockDynamoDBPut();

        const draftData = {
          title: 'Test Draft',
          content: 'This is a draft post content'
        };

        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'POST',
          path: '/posts/draft',
          body: JSON.stringify(draftData)
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(201);
        const body = JSON.parse(result.body);
        expect(body.message).toBe('Draft post created successfully');
        expect(body.post).toBeDefined();
        expect(body.post.status).toBe('draft');
        expect(body.post.active).toBe(false);
        expect(body.post.content).toBe(draftData.content);
        expect(body.post.title).toBe(draftData.title);
      });

      it('should require content for draft post', async () => {
        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'POST',
          path: '/posts/draft',
          body: JSON.stringify({ title: 'Test' })
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(400);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('Content is required');
      });

      it('should require authentication for draft post', async () => {
        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'POST',
          path: '/posts/draft',
          body: JSON.stringify({ content: 'Test content' }),
          requestContext: {} // No authorizer context
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(401);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('User not authenticated');
      });
    });

    describe('PUT /posts/{id}/media', () => {
      it('should attach media to draft post successfully', async () => {
        const testPost = TestDataGenerator.createPost(TEST_USERS.VALID_USER.id, {
          status: 'draft',
          active: false
        });
        const testMedia = TestDataGenerator.createMedia(TEST_USERS.VALID_USER.id, {
          status: 'uploaded'
        });

        mockDynamoDBGet(testMedia); // First call for media verification
        mockDynamoDBGet(testPost);  // Second call for post retrieval
        mockDynamoDBUpdate(testPost); // Update post with media

        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: `/posts/${testPost.id}/media`,
          pathParameters: { id: testPost.id },
          body: JSON.stringify({ media_id: testMedia.id })
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(200);
        const body = JSON.parse(result.body);
        expect(body.message).toBe('Media attached to post successfully');
        expect(body.post).toBeDefined();
      });

      it('should require media_id', async () => {
        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: '/posts/test-id/media',
          pathParameters: { id: 'test-id' },
          body: JSON.stringify({})
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(400);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('media_id is required');
      });

      it('should verify media exists and is uploaded', async () => {
        mockDynamoDBGet(null); // Media not found

        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: '/posts/test-id/media',
          pathParameters: { id: 'test-id' },
          body: JSON.stringify({ media_id: 'invalid-media-id' })
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(400);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('Invalid media_id: media not found');
      });

      it('should verify media upload is completed', async () => {
        const testMedia = TestDataGenerator.createMedia(TEST_USERS.VALID_USER.id, {
          status: 'pending' // Not uploaded yet
        });

        mockDynamoDBGet(testMedia);

        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: '/posts/test-id/media',
          pathParameters: { id: 'test-id' },
          body: JSON.stringify({ media_id: testMedia.id })
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(400);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('Media upload not completed');
      });

      it('should verify post ownership', async () => {
        const testPost = TestDataGenerator.createPost('different-user-id', {
          status: 'draft',
          active: false
        });
        const testMedia = TestDataGenerator.createMedia(TEST_USERS.VALID_USER.id, {
          status: 'uploaded'
        });

        mockDynamoDBGet(testMedia); // Media verification
        mockDynamoDBGet(testPost);  // Post retrieval

        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: `/posts/${testPost.id}/media`,
          pathParameters: { id: testPost.id },
          body: JSON.stringify({ media_id: testMedia.id })
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(403);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('Not authorized to modify this post');
      });
    });

    describe('PUT /posts/{id}/publish', () => {
      it('should publish draft post successfully', async () => {
        const testPost = TestDataGenerator.createPost(TEST_USERS.VALID_USER.id, {
          status: 'draft',
          active: false
        });

        mockDynamoDBGet(testPost);  // Post retrieval
        mockDynamoDBUpdate(testPost); // Update post to published

        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: `/posts/${testPost.id}/publish`,
          pathParameters: { id: testPost.id }
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(200);
        const body = JSON.parse(result.body);
        expect(body.message).toBe('Post published successfully');
        expect(body.post).toBeDefined();
      });

      it('should publish post with media successfully', async () => {
        const testPost = TestDataGenerator.createPost(TEST_USERS.VALID_USER.id, {
          status: 'uploading_media',
          active: false,
          media_id: 'test-media-id'
        });
        const testMedia = TestDataGenerator.createMedia(TEST_USERS.VALID_USER.id, {
          status: 'uploaded'
        });

        mockDynamoDBGet(testPost);  // Post retrieval
        mockDynamoDBGet(testMedia); // Media verification
        mockDynamoDBUpdate(testPost); // Update post to published
        mockDynamoDBGet(testMedia); // Media fetch for response

        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: `/posts/${testPost.id}/publish`,
          pathParameters: { id: testPost.id }
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(200);
        const body = JSON.parse(result.body);
        expect(body.message).toBe('Post published successfully');
        expect(body.post).toBeDefined();
      });

      it('should require authentication', async () => {
        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: '/posts/test-id/publish',
          pathParameters: { id: 'test-id' },
          requestContext: {} // No authorizer context
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(401);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('User not authenticated');
      });

      it('should verify post exists', async () => {
        mockDynamoDBGet(null); // Post not found

        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: '/posts/invalid-id/publish',
          pathParameters: { id: 'invalid-id' }
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(404);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('Post not found');
      });

      it('should verify post ownership', async () => {
        const testPost = TestDataGenerator.createPost('different-user-id', {
          status: 'draft',
          active: false
        });

        mockDynamoDBGet(testPost);

        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: `/posts/${testPost.id}/publish`,
          pathParameters: { id: testPost.id }
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(403);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('Not authorized to modify this post');
      });

      it('should verify media upload completion if post has media', async () => {
        const testPost = TestDataGenerator.createPost(TEST_USERS.VALID_USER.id, {
          status: 'uploading_media',
          active: false,
          media_id: 'test-media-id'
        });
        const testMedia = TestDataGenerator.createMedia(TEST_USERS.VALID_USER.id, {
          status: 'pending' // Not uploaded yet
        });

        mockDynamoDBGet(testPost);  // Post retrieval
        mockDynamoDBGet(testMedia); // Media verification

        const event = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: `/posts/${testPost.id}/publish`,
          pathParameters: { id: testPost.id }
        });
        const context = TestDataGenerator.createLambdaContext();

        const result = await handler(event, context);

        expect(result.statusCode).toBe(400);
        const body = JSON.parse(result.body);
        expect(body.error).toBe('Media upload not completed');
      });
    });

    describe('Integration: Complete Multi-Step Workflow', () => {
      it('should complete full workflow: draft -> attach media -> publish', async () => {
        // Step 1: Create draft post
        mockDynamoDBPut();

        const draftEvent = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'POST',
          path: '/posts/draft',
          body: JSON.stringify({ content: 'Test post content' })
        });
        const context = TestDataGenerator.createLambdaContext();

        const draftResult = await handler(draftEvent, context);
        expect(draftResult.statusCode).toBe(201);

        const draftBody = JSON.parse(draftResult.body);
        const postId = draftBody.post.id;

        // Step 2: Attach media
        const testMedia = TestDataGenerator.createMedia(TEST_USERS.VALID_USER.id, {
          status: 'uploaded'
        });
        const testPost = TestDataGenerator.createPost(TEST_USERS.VALID_USER.id, {
          id: postId,
          status: 'draft',
          active: false
        });

        mockDynamoDBGet(testMedia); // Media verification
        mockDynamoDBGet(testPost);  // Post retrieval
        mockDynamoDBUpdate(testPost); // Update post with media

        const attachEvent = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: `/posts/${postId}/media`,
          pathParameters: { id: postId },
          body: JSON.stringify({ media_id: testMedia.id })
        });

        const attachResult = await handler(attachEvent, context);
        expect(attachResult.statusCode).toBe(200);

        // Step 3: Publish post
        const updatedPost = { ...testPost, media_id: testMedia.id, status: 'uploading_media' };

        mockDynamoDBGet(updatedPost); // Post retrieval
        mockDynamoDBGet(testMedia);   // Media verification
        mockDynamoDBUpdate(updatedPost); // Update post to published
        mockDynamoDBGet(testMedia);   // Media fetch for response

        const publishEvent = TestDataGenerator.createAPIGatewayEvent({
          httpMethod: 'PUT',
          path: `/posts/${postId}/publish`,
          pathParameters: { id: postId }
        });

        const publishResult = await handler(publishEvent, context);
        expect(publishResult.statusCode).toBe(200);

        const publishBody = JSON.parse(publishResult.body);
        expect(publishBody.message).toBe('Post published successfully');
        expect(publishBody.post).toBeDefined();
      });
    });
  });
});
