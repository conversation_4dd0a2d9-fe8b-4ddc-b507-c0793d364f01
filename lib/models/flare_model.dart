// lib/models/flare_model.dart

enum FlareCategory {
  arrows,
  bubbles,
  flexFun,
  memes,
  smack,
  smellies,
}

extension FlareCategoryExtension on FlareCategory {
  String get displayName {
    switch (this) {
      case FlareCategory.arrows:
        return 'Arrows';
      case FlareCategory.bubbles:
        return 'Bubbles';
      case FlareCategory.flexFun:
        return 'Flex Fun';
      case FlareCategory.memes:
        return 'Memes';
      case FlareCategory.smack:
        return 'Smack';
      case FlareCategory.smellies:
        return 'Smellies';
    }
  }

  String get folderName {
    switch (this) {
      case FlareCategory.arrows:
        return 'arrows';
      case FlareCategory.bubbles:
        return 'bubbles';
      case FlareCategory.flexFun:
        return 'flex_fun';
      case FlareCategory.memes:
        return 'memes';
      case FlareCategory.smack:
        return 'smack';
      case FlareCategory.smellies:
        return 'smellies';
    }
  }

  String get assetPath {
    return 'assets/images/flare/${folderName}/';
  }
}

class FlareItem {
  final String name;
  final String fileName;
  final FlareCategory category;

  FlareItem({
    required this.name,
    required this.fileName,
    required this.category,
  });

  String get assetPath {
    return '${category.assetPath}$fileName';
  }

  String get displayName {
    // Remove file extension and clean up the name
    String cleanName = fileName.replaceAll('.png', '');
    // Replace underscores and hyphens with spaces
    cleanName = cleanName.replaceAll('_', ' ').replaceAll('-', ' ');
    // Capitalize first letter of each word
    return cleanName.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FlareItem &&
        other.fileName == fileName &&
        other.category == category;
  }

  @override
  int get hashCode => fileName.hashCode ^ category.hashCode;

  @override
  String toString() {
    return 'FlareItem(name: $name, fileName: $fileName, category: ${category.displayName})';
  }
}

class FlareOverlay {
  final FlareItem item;
  final double x;
  final double y;
  final double scale;
  final double rotation;
  final String id;

  FlareOverlay({
    required this.item,
    required this.x,
    required this.y,
    this.scale = 1.0,
    this.rotation = 0.0,
    required this.id,
  });

  FlareOverlay copyWith({
    FlareItem? item,
    double? x,
    double? y,
    double? scale,
    double? rotation,
    String? id,
  }) {
    return FlareOverlay(
      item: item ?? this.item,
      x: x ?? this.x,
      y: y ?? this.y,
      scale: scale ?? this.scale,
      rotation: rotation ?? this.rotation,
      id: id ?? this.id,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FlareOverlay && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ClipArtOverlay(item: ${item.displayName}, x: $x, y: $y, scale: $scale, rotation: $rotation)';
  }
}
